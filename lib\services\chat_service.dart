import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';

class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _chatsCollection = 'chats';
  static const String _messagesCollection = 'messages';
  static const Uuid _uuid = Uuid();

  /// Create or get existing chat between two users
  static Future<ChatModel?> createOrGetChat({
    required String currentUserId,
    required String otherUserId,
    required String currentUserName,
    required String otherUserName,
  }) async {
    try {
      // Check if chat already exists
      final existingChat = await _findExistingChat(currentUserId, otherUserId);
      if (existingChat != null) {
        return existingChat;
      }

      // Create new chat
      final chatId = _uuid.v4();
      final now = DateTime.now();

      final chat = ChatModel(
        id: chatId,
        participants: [currentUserId, otherUserId],
        lastMessage: '',
        lastMessageSenderId: '',
        lastMessageTime: now,
        unreadCounts: {
          currentUserId: 0,
          otherUserId: 0,
        },
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .set(chat.toMap());

      return chat;
    } catch (e) {
      print('Error creating/getting chat: $e');
      return null;
    }
  }

  /// Find existing chat between two users
  static Future<ChatModel?> _findExistingChat(String userId1, String userId2) async {
    try {
      final querySnapshot = await _firestore
          .collection(_chatsCollection)
          .where('participants', arrayContains: userId1)
          .get();

      for (final doc in querySnapshot.docs) {
        final chat = ChatModel.fromMap(doc.data());
        if (chat.participants.contains(userId2)) {
          return chat;
        }
      }

      return null;
    } catch (e) {
      print('Error finding existing chat: $e');
      return null;
    }
  }

  /// Get user's chats stream
  static Stream<List<ChatModel>> getUserChatsStream(String userId) {
    return _firestore
        .collection(_chatsCollection)
        .where('participants', arrayContains: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ChatModel.fromMap(doc.data()))
            .toList());
  }

  /// Send a message
  static Future<MessageModel?> sendMessage({
    required String chatId,
    required String senderId,
    required String senderName,
    required String content,
    MessageType type = MessageType.text,
  }) async {
    try {
      final messageId = _uuid.v4();
      final now = DateTime.now();

      final message = MessageModel(
        id: messageId,
        chatId: chatId,
        senderId: senderId,
        senderName: senderName,
        content: content,
        type: type,
        createdAt: now,
        readBy: [senderId], // Sender has read the message
      );

      // Save message
      await _firestore
          .collection(_messagesCollection)
          .doc(messageId)
          .set(message.toMap());

      // Update chat's last message
      await _updateChatLastMessage(chatId, content, senderId, now);

      return message;
    } catch (e) {
      print('Error sending message: $e');
      return null;
    }
  }

  /// Update chat's last message
  static Future<void> _updateChatLastMessage(
    String chatId,
    String lastMessage,
    String senderId,
    DateTime timestamp,
  ) async {
    try {
      // Get chat to update unread counts
      final chatDoc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();

      if (chatDoc.exists) {
        final chat = ChatModel.fromMap(chatDoc.data()!);
        final updatedUnreadCounts = Map<String, int>.from(chat.unreadCounts);

        // Increment unread count for other participants
        for (final participantId in chat.participants) {
          if (participantId != senderId) {
            updatedUnreadCounts[participantId] = 
                (updatedUnreadCounts[participantId] ?? 0) + 1;
          }
        }

        await _firestore
            .collection(_chatsCollection)
            .doc(chatId)
            .update({
          'lastMessage': lastMessage,
          'lastMessageSenderId': senderId,
          'lastMessageTime': Timestamp.fromDate(timestamp),
          'unreadCounts': updatedUnreadCounts,
          'updatedAt': Timestamp.fromDate(timestamp),
        });
      }
    } catch (e) {
      print('Error updating chat last message: $e');
    }
  }

  /// Get messages stream for a chat
  static Stream<List<MessageModel>> getMessagesStream(String chatId) {
    return _firestore
        .collection(_messagesCollection)
        .where('chatId', isEqualTo: chatId)
        .where('isDeleted', isEqualTo: false)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MessageModel.fromMap(doc.data()))
            .toList());
  }

  /// Mark messages as read
  static Future<void> markMessagesAsRead({
    required String chatId,
    required String userId,
  }) async {
    try {
      // Get unread messages
      final messagesSnapshot = await _firestore
          .collection(_messagesCollection)
          .where('chatId', isEqualTo: chatId)
          .where('senderId', isNotEqualTo: userId)
          .get();

      final batch = _firestore.batch();

      for (final doc in messagesSnapshot.docs) {
        final message = MessageModel.fromMap(doc.data());
        if (!message.isReadBy(userId)) {
          final updatedReadBy = List<String>.from(message.readBy)..add(userId);
          batch.update(doc.reference, {'readBy': updatedReadBy});
        }
      }

      await batch.commit();

      // Reset unread count for this user in chat
      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .update({
        'unreadCounts.$userId': 0,
      });
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  /// Delete a message
  static Future<bool> deleteMessage(String messageId) async {
    try {
      await _firestore
          .collection(_messagesCollection)
          .doc(messageId)
          .update({'isDeleted': true});
      return true;
    } catch (e) {
      print('Error deleting message: $e');
      return false;
    }
  }

  /// Get chat by ID
  static Future<ChatModel?> getChatById(String chatId) async {
    try {
      final doc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();

      if (doc.exists) {
        return ChatModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting chat by ID: $e');
      return null;
    }
  }

  /// Get other participant info from chat
  static String getOtherParticipantId(ChatModel chat, String currentUserId) {
    return chat.participants.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
  }

  /// Get unread count for user
  static int getUnreadCount(ChatModel chat, String userId) {
    return chat.unreadCounts[userId] ?? 0;
  }
}
