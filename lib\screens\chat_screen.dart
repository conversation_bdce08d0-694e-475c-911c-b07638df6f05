import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Messages'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // TODO: Start new conversation
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              color: AppConstants.surfaceColor,
              border: Border(
                bottom: BorderSide(
                  color: AppConstants.backgroundColor,
                  width: 1,
                ),
              ),
            ),
            child: <PERSON><PERSON><PERSON>(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                prefixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: AppConstants.backgroundColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),

          // Chat List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.only(bottom: 100), // Bottom padding for navigation gap
              itemCount: 15, // Placeholder count
              itemBuilder: (context, index) {
                return _buildChatListItem(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatListItem(int index) {
    final bool isOnline = index % 3 == 0;
    final bool hasUnreadMessages = index % 4 == 0;
    final int unreadCount = hasUnreadMessages ? (index % 5) + 1 : 0;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: AppConstants.primaryColor,
            child: Text(
              'U${index + 1}',
              style: const TextStyle(
                color: AppConstants.onPrimaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (isOnline)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppConstants.surfaceColor,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
      title: Text(
        'User ${index + 1}',
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: hasUnreadMessages ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        _getLastMessage(index),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: hasUnreadMessages
              ? AppConstants.textPrimaryColor
              : AppConstants.textSecondaryColor,
          fontWeight: hasUnreadMessages ? FontWeight.w500 : FontWeight.normal,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            _getTimeString(index),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: hasUnreadMessages
                  ? AppConstants.primaryColor
                  : AppConstants.textSecondaryColor,
              fontWeight: hasUnreadMessages ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          if (hasUnreadMessages) ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                unreadCount.toString(),
                style: const TextStyle(
                  color: AppConstants.onPrimaryColor,
                  fontSize: AppConstants.fontSizeSmall,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
      onTap: () {
        // TODO: Navigate to chat conversation
        _navigateToChatConversation(index);
      },
    );
  }

  String _getLastMessage(int index) {
    final List<String> messages = [
      'Hey, how are you doing?',
      'Thanks for the help!',
      'See you tomorrow',
      'That sounds great!',
      'Can we meet later?',
      'Perfect, let\'s do it',
      'I\'ll send you the details',
      'Looking forward to it',
      'Have a great day!',
      'Talk to you soon',
    ];
    return messages[index % messages.length];
  }

  String _getTimeString(int index) {
    final List<String> times = [
      'now',
      '5m',
      '1h',
      '2h',
      '1d',
      '2d',
      '1w',
    ];
    return times[index % times.length];
  }

  void _navigateToChatConversation(int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatConversationScreen(
          userName: 'User ${index + 1}',
          userId: 'user_${index + 1}',
        ),
      ),
    );
  }
}

class ChatConversationScreen extends StatefulWidget {
  final String userName;
  final String userId;

  const ChatConversationScreen({
    super.key,
    required this.userName,
    required this.userId,
  });

  @override
  State<ChatConversationScreen> createState() => _ChatConversationScreenState();
}

class _ChatConversationScreenState extends State<ChatConversationScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: AppConstants.primaryColor,
              child: Text(
                widget.userName.substring(0, 1),
                style: const TextStyle(
                  color: AppConstants.onPrimaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.userName,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Online',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.videocam_outlined),
            onPressed: () {
              // TODO: Start video call
            },
          ),
          IconButton(
            icon: const Icon(Icons.call_outlined),
            onPressed: () {
              // TODO: Start voice call
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages List
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: 20, // Placeholder count
              itemBuilder: (context, index) {
                return _buildMessageBubble(index);
              },
            ),
          ),

          // Message Input
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              color: AppConstants.surfaceColor,
              border: Border(
                top: BorderSide(
                  color: AppConstants.backgroundColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Type a message...',
                      filled: true,
                      fillColor: AppConstants.backgroundColor,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                        vertical: AppConstants.paddingSmall,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (value) {
                      _sendMessage();
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                IconButton(
                  onPressed: _sendMessage,
                  icon: const Icon(Icons.send),
                  style: IconButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    foregroundColor: AppConstants.onPrimaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(int index) {
    final bool isMe = index % 3 == 0;

    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: isMe ? AppConstants.primaryColor : AppConstants.backgroundColor,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        child: Text(
          'Message ${index + 1}: This is a sample message content.',
          style: TextStyle(
            color: isMe ? AppConstants.onPrimaryColor : AppConstants.textPrimaryColor,
          ),
        ),
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      // TODO: Send message functionality
      _messageController.clear();
    }
  }
}
